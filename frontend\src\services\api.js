import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add correlation ID
api.interceptors.request.use(
  (config) => {
    // Generate correlation ID for request tracking
    const correlationId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    config.headers['X-Request-Id'] = correlationId

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
      window.location.href = '/login'
    }
    
    // Extract error message from response
    const message = error.response?.data?.message || error.message || 'An error occurred'
    
    // Create enhanced error object
    const enhancedError = new Error(message)
    enhancedError.status = error.response?.status
    enhancedError.code = error.response?.data?.code
    enhancedError.correlationId = error.response?.data?.correlationId
    enhancedError.originalError = error
    
    return Promise.reject(enhancedError)
  }
)

// Helper function to generate idempotency key
export const generateIdempotencyKey = () => 'idem_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)

// API methods
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  me: () => api.get('/auth/me'),
}

export const suppliersAPI = {
  getAll: (params) => api.get('/suppliers', { params }),
  getById: (id) => api.get(`/suppliers/${id}`),
  create: (data, idempotencyKey) => api.post('/suppliers', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  update: (id, data, idempotencyKey) => api.put(`/suppliers/${id}`, data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  delete: (id) => api.delete(`/suppliers/${id}`),
}

export const customersAPI = {
  getAll: (params) => api.get('/customers', { params }),
  getById: (id) => api.get(`/customers/${id}`),
  create: (data, idempotencyKey) => api.post('/customers', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  update: (id, data, idempotencyKey) => api.put(`/customers/${id}`, data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  delete: (id) => api.delete(`/customers/${id}`),
}

export const productsAPI = {
  getAll: (params) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  create: (data, idempotencyKey) => api.post('/products', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  update: (id, data, idempotencyKey) => api.put(`/products/${id}`, data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  delete: (id) => api.delete(`/products/${id}`),
}

export const purchasesAPI = {
  getAll: (params) => api.get('/purchases', { params }),
  getById: (id) => api.get(`/purchases/${id}`),
  create: (data, idempotencyKey) => api.post('/purchases', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  update: (id, data, idempotencyKey) => api.put(`/purchases/${id}`, data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
}

export const salesAPI = {
  getAll: (params) => api.get('/sales', { params }),
  getById: (id) => api.get(`/sales/${id}`),
  create: (data, idempotencyKey) => api.post('/sales', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
}

export const stockAPI = {
  getAll: (params) => api.get('/stock', { params }),
  getByProduct: (productId, params) => api.get(`/stock/product/${productId}`, { params }),
  getSummary: () => api.get('/stock/summary'),
}

export const movementsAPI = {
  getAll: (params) => api.get('/movements', { params }),
  getById: (id) => api.get(`/movements/${id}`),
  getSummaryByType: (params) => api.get('/movements/summary/by-type', { params }),
  getSummaryByProduct: (params) => api.get('/movements/summary/by-product', { params }),
  getTypes: () => api.get('/movements/types'),
}

export const reportsAPI = {
  getDailySummaries: (params) => api.get('/reports/daily-summaries', { params }),
  generateDailySummary: (data, idempotencyKey) => api.post('/reports/daily-summaries/generate', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  generateSummariesRange: (data, idempotencyKey) => api.post('/reports/daily-summaries/generate-range', data, {
    headers: { 'Idempotency-Key': idempotencyKey }
  }),
  getProfitLoss: (params) => api.get('/reports/profit-loss', { params }),
  getSalesByProduct: (params) => api.get('/reports/sales-by-product', { params }),
  getInventoryTurnover: (params) => api.get('/reports/inventory-turnover', { params }),
  getDashboardMetrics: () => api.get('/reports/dashboard-metrics'),
}

export const importsAPI = {
  getBatches: (params) => api.get('/imports/batches', { params }),
  getBatch: (id) => api.get(`/imports/batches/${id}`),
  createBatch: (formData) => api.post('/imports/batches', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  stageBatch: (id) => api.post(`/imports/batches/${id}/stage`),
  getBatchTransactions: (id, params) => api.get(`/imports/batches/${id}/transactions`, { params }),
  getBatchSummary: (id) => api.get(`/imports/batches/${id}/summary`),
  updateTransaction: (id, data) => api.put(`/imports/transactions/${id}`, data),
  markBatchReady: (id) => api.post(`/imports/batches/${id}/ready`),
  commitBatch: (id) => api.post(`/imports/batches/${id}/commit`),
  rollbackBatch: (id) => api.post(`/imports/batches/${id}/rollback`),
}

export default api
