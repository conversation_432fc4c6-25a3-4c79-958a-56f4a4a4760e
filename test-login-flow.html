<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Meat Shop Login Flow Test</h1>
    
    <div class="test-section">
        <h2>Test 1: API Login</h2>
        <button onclick="testLogin()">Test Login API</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Auth Me Endpoint</h2>
        <button onclick="testAuthMe()">Test Auth Me API</button>
        <div id="auth-me-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Frontend Application</h2>
        <p>Open <a href="http://localhost:3002" target="_blank">http://localhost:3002</a> and try logging in with:</p>
        <ul>
            <li>Username: admin</li>
            <li>Password: admin123</li>
        </ul>
        <p>Expected behavior: After login, you should be redirected to the dashboard.</p>
    </div>

    <script>
        let authToken = null;

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    authToken = data.token;
                    resultDiv.innerHTML = `
                        <div class="success">✅ Login successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Login failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            }
        }

        async function testAuthMe() {
            const resultDiv = document.getElementById('auth-me-result');
            
            if (!authToken) {
                resultDiv.innerHTML = '<div class="error">❌ Please run login test first to get token</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Auth Me successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Auth Me failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
