import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { stockAPI, movementsAPI, reportsAPI } from '../services/api'
import { shouldUseMockData, mockAPI } from '../services/mockData'
import {
  ArchiveBoxIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline'

export default function Dashboard() {
  const [stockSummary, setStockSummary] = useState(null)
  const [recentMovements, setRecentMovements] = useState([])
  const [dashboardMetrics, setDashboardMetrics] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError('')

      // Check if we should use mock data (when backend is not available)
      if (shouldUseMockData()) {
        console.log('Using mock data for dashboard (backend not available)')

        // Use mock API
        const [stockResponse, movementsResponse, metricsResponse] = await Promise.all([
          mockAPI.stock.getSummary(),
          mockAPI.movements.getAll(),
          mockAPI.reports.getDashboardMetrics()
        ])

        setStockSummary(stockResponse.data)
        setRecentMovements(movementsResponse.data.items)
        setDashboardMetrics(metricsResponse.data)
      } else {
        // Load stock summary, recent movements, and dashboard metrics in parallel
        const [stockResponse, movementsResponse, metricsResponse] = await Promise.all([
          stockAPI.getSummary(),
          movementsAPI.getAll({ page: 1, limit: 10 }),
          reportsAPI.getDashboardMetrics().catch(() => ({ data: null })) // Don't fail if no data
        ])

        setStockSummary(stockResponse.data)
        setRecentMovements(movementsResponse.data.items)
        setDashboardMetrics(metricsResponse.data)
      }
    } catch (err) {
      console.error('Dashboard data loading error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatWeight = (weight) => {
    return `${parseFloat(weight).toFixed(3)} kg`
  }

  const getMovementIcon = (type) => {
    switch (type) {
      case 'PURCHASE':
        return <ArrowTrendingUpIcon className="h-5 w-5 text-green-500" />
      case 'SALE':
        return <ArrowTrendingDownIcon className="h-5 w-5 text-red-500" />
      default:
        return <ArchiveBoxIcon className="h-5 w-5 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">Error loading dashboard: {error}</div>
      </div>
    )
  }

  const stats = [
    {
      name: 'Total Items',
      value: stockSummary?.overall?.totalItems || 0,
      icon: ArchiveBoxIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Total Stock',
      value: formatWeight(stockSummary?.overall?.totalQtyKg || 0),
      icon: ArchiveBoxIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Stock Value',
      value: formatCurrency(stockSummary?.overall?.totalValue || 0),
      icon: CurrencyDollarIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      name: 'Low Stock Items',
      value: stockSummary?.overall?.lowStockItems || 0,
      icon: ExclamationTriangleIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Development Notice */}
      {shouldUseMockData() && (
        <div className="rounded-md bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Development Mode
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Backend server is not available. Using mock data for demonstration.
                  Start the backend server to see real data.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-md ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Financial Metrics (if available) */}
      {dashboardMetrics && (
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Financial Performance (Last {dashboardMetrics.period.days} days)
          </h2>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            <div className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatCurrency(dashboardMetrics.metrics.totalRevenue)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Profit</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatCurrency(dashboardMetrics.metrics.totalProfit)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Profit Margin</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {dashboardMetrics.metrics.overallMargin.toFixed(2)}%
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Alerts */}
      {stockSummary?.overall?.lowStockItems > 0 && (
        <div className="rounded-md bg-yellow-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Low Stock Alert
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  You have {stockSummary.overall.lowStockItems} items with low stock levels.{' '}
                  <Link to="/stock?lowStock=true" className="font-medium underline">
                    View low stock items
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {stockSummary?.overall?.negativeStockItems > 0 && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Negative Stock Alert
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  You have {stockSummary.overall.negativeStockItems} items with negative stock levels.{' '}
                  <Link to="/stock" className="font-medium underline">
                    View stock levels
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Stock by Category */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Stock by Category</h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {stockSummary?.byCategory?.slice(0, 5).map((category) => (
                <div key={category._id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {category._id || 'Uncategorized'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {category.totalItems} items
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatWeight(category.totalQtyKg)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(category.totalValue)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Link
                to="/stock"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                View all stock →
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Movements */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Movements</h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {recentMovements.slice(0, 5).map((movement) => (
                <div key={movement._id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {getMovementIcon(movement.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {movement.productId?.name || 'Unknown Product'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {movement.type} • {formatWeight(Math.abs(movement.kg))}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {new Date(movement.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Link
                to="/movements"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                View all movements →
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <Link
              to="/purchases"
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <TruckIcon className="h-5 w-5 mr-2 text-gray-400" />
              New Purchase
            </Link>
            <Link
              to="/sales"
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <CurrencyDollarIcon className="h-5 w-5 mr-2 text-gray-400" />
              New Sale
            </Link>
            <Link
              to="/stock"
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArchiveBoxIcon className="h-5 w-5 mr-2 text-gray-400" />
              View Stock
            </Link>
            <Link
              to="/movements"
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowTrendingUpIcon className="h-5 w-5 mr-2 text-gray-400" />
              View Movements
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
