import React, { createContext, useContext, useState, useEffect } from 'react'
import api from '../services/api.js'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is logged in on app start
    const token = localStorage.getItem('token')
    const userData = localStorage.getItem('user')

    if (token && userData) {
      try {
        const user = JSON.parse(userData)
        setUser(user)
        // Set the token in axios defaults
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      } catch (error) {
        // If parsing fails, clear storage
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    }

    // Optional: Verify token is still valid by making a test request
    // Only if backend is available - but don't block app loading
    if (token && userData) {
      // Set a timeout to prevent hanging indefinitely
      const timeoutId = setTimeout(() => {
        setLoading(false)
      }, 2000) // 2 second timeout

      api.get('/auth/me')
        .then(response => {
          clearTimeout(timeoutId)
          setUser(response.data.user)
        })
        .catch((error) => {
          clearTimeout(timeoutId)
          console.warn('Backend not available or token invalid:', error.message)
          // Don't clear token immediately - backend might be temporarily down
          // Only clear if it's a 401 (unauthorized) error
          if (error.status === 401) {
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            setUser(null)
            delete api.defaults.headers.common['Authorization']
          }
        })
        .finally(() => {
          clearTimeout(timeoutId)
          setLoading(false)
        })
    } else {
      setLoading(false)
    }
  }, [])

  const login = async (username, password) => {
    try {
      // Try API login first
      const response = await api.post('/auth/login', {
        username,
        password
      })

      const { token, user: userData } = response.data

      // Store token and user in localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(userData))

      // Set token in axios defaults
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`

      // Set user state
      setUser(userData)

      return { success: true }
    } catch (error) {
      console.error('Login error:', error)

      // Check if it's a network error (backend not available)
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error') || !error.response) {
        // For development: allow demo login when backend is not available
        if ((username === 'admin' && password === 'admin123') ||
            (username === 'storekeeper' && password === 'store123')) {

          const mockUser = {
            id: username === 'admin' ? 1 : 2,
            name: username === 'admin' ? 'Admin User' : 'Storekeeper User',
            username: username,
            role: username === 'admin' ? 'admin' : 'storekeeper'
          }

          const mockToken = 'mock-token-' + Date.now()

          // Store mock data
          localStorage.setItem('token', mockToken)
          localStorage.setItem('user', JSON.stringify(mockUser))

          // Set user state
          setUser(mockUser)

          console.warn('Backend not available - using mock authentication for development')
          return { success: true }
        }

        return {
          success: false,
          message: 'Backend server is not available. Please check if the server is running.'
        }
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Login failed'
      }
    }
  }

  const logout = async () => {
    try {
      // Call logout endpoint
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local state regardless of API call success
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
      setUser(null)
    }
  }

  const value = {
    user,
    login,
    logout,
    loading,
    isAdmin: user?.role === 'admin',
    isStorekeeper: user?.role === 'storekeeper'
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
