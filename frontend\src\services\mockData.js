// Mock data for development when backend is not available

export const mockStockSummary = {
  overall: {
    totalItems: 25,
    totalQtyKg: 1250.5,
    totalValue: 45000.75,
    lowStockItems: 3,
    negativeStockItems: 0
  },
  byCategory: [
    {
      _id: 'Beef',
      totalItems: 8,
      totalQtyKg: 450.2,
      totalValue: 18000.50
    },
    {
      _id: 'Chicken',
      totalItems: 6,
      totalQtyKg: 300.8,
      totalValue: 9500.25
    },
    {
      _id: 'Lamb',
      totalItems: 5,
      totalQtyKg: 250.3,
      totalValue: 12000.00
    },
    {
      _id: 'Fish',
      totalItems: 4,
      totalQtyKg: 180.1,
      totalValue: 4200.00
    },
    {
      _id: 'Other',
      totalItems: 2,
      totalQtyKg: 69.1,
      totalValue: 1300.00
    }
  ]
}

export const mockRecentMovements = [
  {
    _id: '1',
    type: 'PURCHASE',
    productId: { name: 'Premium Beef Ribeye' },
    kg: 25.5,
    date: new Date().toISOString()
  },
  {
    _id: '2',
    type: 'SALE',
    productId: { name: 'Chicken Breast' },
    kg: -12.3,
    date: new Date(Date.now() - 86400000).toISOString()
  },
  {
    _id: '3',
    type: 'PURCHASE',
    productId: { name: 'Fresh Salmon' },
    kg: 15.8,
    date: new Date(Date.now() - 172800000).toISOString()
  },
  {
    _id: '4',
    type: 'SALE',
    productId: { name: 'Lamb Chops' },
    kg: -8.2,
    date: new Date(Date.now() - 259200000).toISOString()
  },
  {
    _id: '5',
    type: 'PURCHASE',
    productId: { name: 'Ground Beef' },
    kg: 30.0,
    date: new Date(Date.now() - 345600000).toISOString()
  }
]

export const mockDashboardMetrics = {
  period: { days: 30 },
  metrics: {
    totalRevenue: 125000.50,
    totalProfit: 25000.10,
    overallMargin: 20.0
  }
}

// Mock API responses
export const createMockResponse = (data) => ({
  data: data
})

// Check if we should use mock data (when backend is not available)
export const shouldUseMockData = () => {
  // Check if we're in development and have a mock token
  return process.env.NODE_ENV === 'development' && 
         localStorage.getItem('token')?.startsWith('mock-token-')
}

// Mock API implementations
export const mockAPI = {
  stock: {
    getSummary: () => Promise.resolve(createMockResponse(mockStockSummary))
  },
  movements: {
    getAll: () => Promise.resolve(createMockResponse({ items: mockRecentMovements }))
  },
  reports: {
    getDashboardMetrics: () => Promise.resolve(createMockResponse(mockDashboardMetrics))
  }
}
