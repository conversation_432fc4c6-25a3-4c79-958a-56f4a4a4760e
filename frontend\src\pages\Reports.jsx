import React, { useState, useEffect } from 'react'
import { reportsAPI, generateIdempotencyKey } from '../services/api'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  DocumentChartBarIcon,
} from '@heroicons/react/24/outline'

export default function Reports() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [dateRange, setDateRange] = useState({
    from: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    to: format(endOfMonth(new Date()), 'yyyy-MM-dd')
  })
  const [dashboardMetrics, setDashboardMetrics] = useState(null)
  const [profitLossData, setProfitLossData] = useState(null)
  const [salesByProduct, setSalesByProduct] = useState([])
  const [inventoryTurnover, setInventoryTurnover] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (activeTab === 'dashboard') {
      loadDashboardMetrics()
    }
  }, [activeTab])

  useEffect(() => {
    if (activeTab !== 'dashboard') {
      loadReportData()
    }
  }, [activeTab, dateRange])

  const loadDashboardMetrics = async () => {
    try {
      setLoading(true)
      const response = await reportsAPI.getDashboardMetrics()
      setDashboardMetrics(response.data)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const loadReportData = async () => {
    try {
      setLoading(true)
      setError('')

      const params = {
        from: dateRange.from,
        to: dateRange.to
      }

      switch (activeTab) {
        case 'profit-loss':
          const plResponse = await reportsAPI.getProfitLoss(params)
          setProfitLossData(plResponse.data)
          break
        case 'sales-by-product':
          const salesResponse = await reportsAPI.getSalesByProduct(params)
          setSalesByProduct(salesResponse.data)
          break
        case 'inventory-turnover':
          const turnoverResponse = await reportsAPI.getInventoryTurnover(params)
          setInventoryTurnover(turnoverResponse.data)
          break
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatWeight = (weight) => {
    return `${parseFloat(weight).toFixed(3)} kg`
  }

  const formatPercentage = (value) => {
    return `${parseFloat(value).toFixed(2)}%`
  }

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: ChartBarIcon },
    { id: 'profit-loss', name: 'P&L Report', icon: CurrencyDollarIcon },
    { id: 'sales-by-product', name: 'Sales by Product', icon: ArrowTrendingUpIcon },
    { id: 'inventory-turnover', name: 'Inventory Turnover', icon: ArrowTrendingDownIcon },
  ]

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const generateDailySummary = async () => {
    try {
      setLoading(true)
      const idempotencyKey = generateIdempotencyKey()
      await reportsAPI.generateDailySummary(
        { date: new Date().toISOString().split('T')[0] },
        idempotencyKey
      )
      // Reload dashboard metrics
      if (activeTab === 'dashboard') {
        await loadDashboardMetrics()
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Date Range Selector (for non-dashboard tabs) */}
      {activeTab !== 'dashboard' && (
        <div className="card">
          <div className="card-body">
            <div className="flex items-center space-x-4">
              <div>
                <label className="form-label">From Date</label>
                <input
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => handleDateRangeChange('from', e.target.value)}
                  className="form-input"
                />
              </div>
              <div>
                <label className="form-label">To Date</label>
                <input
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => handleDateRangeChange('to', e.target.value)}
                  className="form-input"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={loadReportData}
                  disabled={loading}
                  className="btn-primary"
                >
                  {loading ? 'Loading...' : 'Generate Report'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      )}

      {/* Dashboard Tab */}
      {activeTab === 'dashboard' && dashboardMetrics && !loading && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            <div className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Revenue ({dashboardMetrics.period.days} days)
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatCurrency(dashboardMetrics.metrics.totalRevenue)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Profit
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatCurrency(dashboardMetrics.metrics.totalProfit)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Overall Margin
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatPercentage(dashboardMetrics.metrics.overallMargin)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Actions</h3>
            </div>
            <div className="card-body">
              <button
                onClick={generateDailySummary}
                disabled={loading}
                className="btn-primary"
              >
                <DocumentChartBarIcon className="h-5 w-5 mr-2" />
                Generate Today's Summary
              </button>
            </div>
          </div>
        </div>
      )}

      {/* P&L Report Tab */}
      {activeTab === 'profit-loss' && profitLossData && !loading && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="card">
              <div className="card-body text-center">
                <dt className="text-sm font-medium text-gray-500">Total Revenue</dt>
                <dd className="text-2xl font-bold text-green-600">
                  {formatCurrency(profitLossData.summary.totalRevenue)}
                </dd>
              </div>
            </div>
            <div className="card">
              <div className="card-body text-center">
                <dt className="text-sm font-medium text-gray-500">Total COGS</dt>
                <dd className="text-2xl font-bold text-red-600">
                  {formatCurrency(profitLossData.summary.totalCogs)}
                </dd>
              </div>
            </div>
            <div className="card">
              <div className="card-body text-center">
                <dt className="text-sm font-medium text-gray-500">Gross Profit</dt>
                <dd className="text-2xl font-bold text-blue-600">
                  {formatCurrency(profitLossData.summary.totalProfit)}
                </dd>
              </div>
            </div>
            <div className="card">
              <div className="card-body text-center">
                <dt className="text-sm font-medium text-gray-500">Margin</dt>
                <dd className="text-2xl font-bold text-purple-600">
                  {formatPercentage(profitLossData.summary.overallMargin)}
                </dd>
              </div>
            </div>
          </div>

          {/* Daily Breakdown */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Daily Breakdown</h3>
            </div>
            <div className="card-body">
              <div className="overflow-x-auto">
                <table className="table">
                  <thead className="table-header">
                    <tr>
                      <th className="table-header-cell">Date</th>
                      <th className="table-header-cell">Revenue</th>
                      <th className="table-header-cell">COGS</th>
                      <th className="table-header-cell">Profit</th>
                      <th className="table-header-cell">Margin</th>
                      <th className="table-header-cell">Losses</th>
                    </tr>
                  </thead>
                  <tbody className="table-body">
                    {profitLossData.dailyData.map((day, index) => (
                      <tr key={index}>
                        <td className="table-cell">
                          {format(new Date(day.date), 'MMM dd, yyyy')}
                        </td>
                        <td className="table-cell">{formatCurrency(day.revenue)}</td>
                        <td className="table-cell">{formatCurrency(day.cogs)}</td>
                        <td className="table-cell">
                          <span className={day.grossProfit >= 0 ? 'text-green-600' : 'text-red-600'}>
                            {formatCurrency(day.grossProfit)}
                          </span>
                        </td>
                        <td className="table-cell">
                          <span className={day.margin >= 0 ? 'text-green-600' : 'text-red-600'}>
                            {formatPercentage(day.margin)}
                          </span>
                        </td>
                        <td className="table-cell">{formatWeight(day.lossesKg)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sales by Product Tab */}
      {activeTab === 'sales-by-product' && salesByProduct.length > 0 && !loading && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Sales Performance by Product</h3>
          </div>
          <div className="card-body">
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Product</th>
                    <th className="table-header-cell">SKU</th>
                    <th className="table-header-cell">Qty Sold</th>
                    <th className="table-header-cell">Revenue</th>
                    <th className="table-header-cell">COGS</th>
                    <th className="table-header-cell">Profit</th>
                    <th className="table-header-cell">Margin</th>
                    <th className="table-header-cell">Avg Price</th>
                    <th className="table-header-cell">Transactions</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {salesByProduct.map((product, index) => (
                    <tr key={index}>
                      <td className="table-cell font-medium">{product.productName}</td>
                      <td className="table-cell">{product.productSku}</td>
                      <td className="table-cell">{formatWeight(product.totalKg)}</td>
                      <td className="table-cell">{formatCurrency(product.totalRevenue)}</td>
                      <td className="table-cell">{formatCurrency(product.totalCogs)}</td>
                      <td className="table-cell">
                        <span className={product.profit >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {formatCurrency(product.profit)}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className={product.margin >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {formatPercentage(product.margin)}
                        </span>
                      </td>
                      <td className="table-cell">{formatCurrency(product.avgSellingPrice)}</td>
                      <td className="table-cell">{product.transactionCount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Inventory Turnover Tab */}
      {activeTab === 'inventory-turnover' && inventoryTurnover.length > 0 && !loading && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Inventory Turnover Analysis</h3>
          </div>
          <div className="card-body">
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Product</th>
                    <th className="table-header-cell">Sold</th>
                    <th className="table-header-cell">Current Stock</th>
                    <th className="table-header-cell">Avg Inventory Value</th>
                    <th className="table-header-cell">Turnover Ratio</th>
                    <th className="table-header-cell">Days in Inventory</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {inventoryTurnover.map((item, index) => (
                    <tr key={index}>
                      <td className="table-cell font-medium">{item.productName}</td>
                      <td className="table-cell">{formatWeight(item.totalSoldKg)}</td>
                      <td className="table-cell">{formatWeight(item.currentStockKg)}</td>
                      <td className="table-cell">{formatCurrency(item.avgInventoryValue)}</td>
                      <td className="table-cell">
                        <span className={item.turnoverRatio >= 1 ? 'text-green-600' : 'text-yellow-600'}>
                          {item.turnoverRatio.toFixed(2)}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className={item.daysInInventory <= 30 ? 'text-green-600' : 'text-red-600'}>
                          {item.daysInInventory} days
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* No Data State */}
      {!loading && (
        (activeTab === 'profit-loss' && !profitLossData) ||
        (activeTab === 'sales-by-product' && salesByProduct.length === 0) ||
        (activeTab === 'inventory-turnover' && inventoryTurnover.length === 0)
      ) && (
        <div className="card">
          <div className="card-body text-center py-12">
            <DocumentChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
            <p className="mt-1 text-sm text-gray-500">
              No data found for the selected date range. Try adjusting the dates or generate daily summaries first.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
